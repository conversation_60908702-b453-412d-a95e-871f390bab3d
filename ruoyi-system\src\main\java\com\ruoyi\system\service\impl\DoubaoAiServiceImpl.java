package com.ruoyi.system.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.system.service.IAiService;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;


/**
 * 火山引擎Doubao服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class DoubaoAiServiceImpl implements IAiService
{
    private static final Logger log = LoggerFactory.getLogger(DoubaoAiServiceImpl.class);

    @Value("${doubao.ai.api-key}")
    private String apiKey;

    @Value("${doubao.ai.base-url:https://ark.cn-beijing.volces.com/api/v3/chat/completions}")
    private String baseUrl;

    @Value("${doubao.ai.model:doubao-seed-1-6-flash-250715}")
    private String model;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 令牌缓存
    private String cachedAccessToken;
    private long tokenExpiresAt;

    /**
     * 获取访问令牌 (火山引擎Doubao使用Bearer token，直接返回API Key)
     */
    @Override
    public synchronized String getAccessToken() {
        // 火山引擎Doubao使用Bearer token认证，直接返回API Key
        log.debug("火山引擎Doubao使用Bearer token认证，返回API Key");
        return apiKey;
    }

    /**
     * 生成文案 (使用火山引擎Doubao OpenAI格式API)
     */
    @Override
    public String generateCopywriting(String prompt, String shopDetails, int maxTokens) {
        try {
            log.info("开始调用火山引擎Doubao生成文案，提示词: {}", prompt);

            // 构建请求体 (OpenAI格式)
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);

            // 构建消息
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");

            // 组合完整的提示词
            String fullPrompt = buildFullPrompt(prompt, shopDetails);
            message.put("content", fullPrompt);

            requestBody.put("messages", new Object[]{message});
            requestBody.put("stream", false);
            if (maxTokens > 0) {
                requestBody.put("max_tokens", maxTokens);
            }

            // 调试信息
            log.info("发送请求到: {}", baseUrl);
            log.info("API Key: {}...", apiKey.substring(0, Math.min(20, apiKey.length())));
            log.info("请求体: {}", requestBody);

            // 使用OkHttp发送请求到DeepSeek-V3接口
            OkHttpClient client = new OkHttpClient();

            // 创建请求体
            String jsonBody = objectMapper.writeValueAsString(requestBody);
            RequestBody body = RequestBody.create(jsonBody, MediaType.parse("application/json"));

            // 创建请求
            Request request = new Request.Builder()
                .url(baseUrl)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + apiKey)
                .addHeader("User-Agent", "Java/1.8")
                .post(body)
                .build();

            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();

                if (response.isSuccessful()) {
                    JsonNode jsonResponse = objectMapper.readTree(responseBody);

                    // 解析OpenAI格式响应
                    if (jsonResponse.has("choices") && jsonResponse.get("choices").isArray() &&
                        jsonResponse.get("choices").size() > 0) {
                        JsonNode choice = jsonResponse.get("choices").get(0);
                        if (choice.has("message") && choice.get("message").has("content")) {
                            String content = choice.get("message").get("content").asText();
                            log.info("DeepSeek-V3接口调用成功，生成文案长度: {}", content.length());
                            return content.trim();
                        }
                    }

                    log.error("DeepSeek-V3接口响应格式异常");
                    log.error("响应内容: {}", responseBody);
                    throw new RuntimeException("AI接口响应格式异常");
                }

                log.error("DeepSeek-V3接口调用失败，状态码: {}", response.code());
                log.error("响应内容: {}", responseBody);
                throw new RuntimeException("AI接口调用失败");
            }

        } catch (Exception e) {
            log.error("调用DeepSeek-V3接口生成文案失败", e);
            throw new RuntimeException("文案生成失败: " + e.getMessage());
        }
    }

    /**
     * 批量生成文案 - 确保每条都不同
     */
    @Override
    public String[] batchGenerateCopywriting(String prompt, String shopDetails, int count, int maxTokens) {
        String[] results = new String[count];

        // 分析店铺类型
        String shopType = analyzeShopType(shopDetails);

        for (int i = 0; i < count; i++) {
            try {
                // 为每次生成创建完全不同的prompt
                String uniquePrompt = buildUniquePrompt(prompt, shopDetails, shopType, i + 1, count);

                log.info("第{}条文案生成，使用prompt: {}", i + 1, uniquePrompt.substring(0, Math.min(100, uniquePrompt.length())) + "...");

                results[i] = generateCopywritingWithUniquePrompt(uniquePrompt, maxTokens);

                // 避免请求过于频繁
                if (i < count - 1) {
                    Thread.sleep(1500); // 间隔1.5秒
                }
            } catch (Exception e) {
                log.error("批量生成第{}条文案失败", i + 1, e);
                results[i] = "生成失败: " + e.getMessage();
            }
        }

        return results;
    }

    /**
     * 为每条文案构建独特的prompt
     */
    private String buildUniquePrompt(String originalPrompt, String shopDetails, String shopType, int index, int total) {
        if ("spa".equals(shopType)) {
            return buildUniqueSpaPrompt(shopDetails, index, total);
        } else {
            return buildUniqueGeneralPrompt(originalPrompt, shopDetails, index, total);
        }
    }

    /**
     * 为足疗SPA构建独特的prompt
     */
    private String buildUniqueSpaPrompt(String shopDetails, int index, int total) {
        // 不同的体验场景
        String[] scenarios = {
            "和朋友聚餐后顺路体验",
            "加班疲劳后放松",
            "周末和闺蜜一起",
            "同事推荐来试试",
            "路过看到进来体验",
            "朋友生日一起来",
            "工作压力大来放松",
            "听说不错专门来的"
        };

        // 不同的关注点
        String[] focuses = {
            "技师手法和服务",
            "环境氛围和舒适度",
            "小食茶点的搭配",
            "性价比和整体感受",
            "放松效果和体验",
            "设施环境和服务",
            "专业程度和效果",
            "整体体验和推荐"
        };

        // 不同的语言风格
        String[] styles = {
            "轻松随意的朋友聊天风格",
            "简洁直接的体验分享",
            "详细描述的感受记录",
            "对比评价的客观描述",
            "推荐安利的热情分享",
            "中性客观的体验评价",
            "感性描述的情感表达",
            "实用导向的经验分享"
        };

        String scenario = scenarios[(index - 1) % scenarios.length];
        String focus = focuses[(index - 1) % focuses.length];
        String style = styles[(index - 1) % styles.length];

        return String.format(
            "你是专业的点评文案专家，请为足疗SPA店铺生成真实的顾客体验文案。\n\n" +
            "店铺信息：%s\n\n" +
            "这是第%d条文案（共%d条），请确保与其他文案完全不同。\n\n" +
            "体验场景：%s\n" +
            "关注重点：%s\n" +
            "语言风格：%s\n\n" +
            "要求：\n" +
            "1. 模拟真实顾客体验，语言自然接地气\n" +
            "2. 突出放松舒适的感受\n" +
            "3. 可以提到具体的服务项目和感受\n" +
            "4. 字数控制在50-80字\n" +
            "5. 每条文案都要有独特的表达方式\n" +
            "6. 适当使用口语化表达\n\n" +
            "参考风格（但不要模仿）：\n" +
            "- 跟朋友吃饭顺路试水悦湾，壹品足道70分钟超爽，金牌技师按得准，西瓜甜，下次想试带艾灸的。\n" +
            "- 加班背痛，水悦湾SPA70分钟松快，结节揉开，芒果西米露好吃，精油味舒服，性价比可。\n\n" +
            "请生成完全不同的点评文案：",
            shopDetails, index, total, scenario, focus, style
        );
    }

    /**
     * 为通用店铺构建独特的prompt
     */
    private String buildUniqueGeneralPrompt(String originalPrompt, String shopDetails, int index, int total) {
        StringBuilder promptBuilder = new StringBuilder();

        promptBuilder.append(String.format("你是专业的文案创作专家，请生成第%d条文案（共%d条）。\n\n", index, total));
        promptBuilder.append("店铺信息：").append(shopDetails).append("\n\n");

        promptBuilder.append("文案要求：\n");
        promptBuilder.append("1. 根据店铺信息生成营销文案\n");
        promptBuilder.append("2. 文案要生动有趣，吸引目标客户\n");
        promptBuilder.append("3. 突出店铺特色和优势\n");
        promptBuilder.append("4. 语言自然，符合目标群体\n");

        // 解析用户提示词中的关键要求，转化为系统指令
        if (originalPrompt != null && !originalPrompt.trim().isEmpty()) {
            String cleanedPrompt = originalPrompt.trim();
            if (cleanedPrompt.contains("模糊介绍") || cleanedPrompt.contains("不要发散")) {
                promptBuilder.append("5. 只基于提供的店铺信息进行创作，不添加未提及的内容\n");
            }
            if (cleanedPrompt.contains("朋友推荐") || cleanedPrompt.contains("推荐语气")) {
                promptBuilder.append("6. 使用朋友推荐的亲切语气\n");
            }
            if (cleanedPrompt.contains("网络热词") || cleanedPrompt.contains("热词")) {
                promptBuilder.append("7. 适当融入网络热词，增加时尚感\n");
            }
        }

        promptBuilder.append(String.format("\n重要：这是第%d条文案，必须与前面的文案完全不同，要有独特的表达方式和内容重点。\n\n", index));
        promptBuilder.append("请直接输出文案内容：");

        return promptBuilder.toString();
    }

    /**
     * 使用独特prompt生成文案
     */
    private String generateCopywritingWithUniquePrompt(String uniquePrompt, int maxTokens) throws Exception {
        // 构建请求体 (OpenAI格式)
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", model);

        // 构建消息
        Map<String, Object> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", uniquePrompt);

        requestBody.put("messages", new Object[]{message});
        requestBody.put("stream", false);
        if (maxTokens > 0) {
            requestBody.put("max_tokens", maxTokens);
        }

        // 使用OkHttp发送请求到DeepSeek-V3接口
        OkHttpClient client = new OkHttpClient();

        // 创建请求体
        String jsonBody = objectMapper.writeValueAsString(requestBody);
        RequestBody body = RequestBody.create(jsonBody, MediaType.parse("application/json"));

        // 创建请求
        Request request = new Request.Builder()
            .url(baseUrl)
            .addHeader("Content-Type", "application/json")
            .addHeader("Authorization", "Bearer " + apiKey)
            .addHeader("User-Agent", "Java/1.8")
            .post(body)
            .build();

        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();

            if (response.isSuccessful()) {
                JsonNode jsonResponse = objectMapper.readTree(responseBody);

                // 解析OpenAI格式响应
                if (jsonResponse.has("choices") && jsonResponse.get("choices").isArray() &&
                    jsonResponse.get("choices").size() > 0) {
                    JsonNode choice = jsonResponse.get("choices").get(0);
                    if (choice.has("message") && choice.get("message").has("content")) {
                        String content = choice.get("message").get("content").asText();
                        return content.trim();
                    }
                }

                log.error("DeepSeek-V3接口响应格式异常");
                throw new RuntimeException("AI接口响应格式异常");
            } else {
                log.error("DeepSeek-V3接口调用失败，状态码: {}, 响应: {}", response.code(), responseBody);
                throw new RuntimeException("文案生成失败: " + response.code());
            }
        }
    }

    /**
     * 构建完整的提示词 - 根据平台类型生成不同风格的文案
     */
    private String buildFullPrompt(String prompt, String shopDetails) {
        StringBuilder fullPrompt = new StringBuilder();

        // 默认的基础提示词
        fullPrompt.append("现在你是一位优秀的文案策划，你需要根据我提供的内容生成一篇口播文案。\n\n");

        // 店铺信息
        fullPrompt.append("店铺信息：").append(shopDetails != null ? shopDetails : "").append("\n\n");

        // 用户自定义提示词
        if (prompt != null && !prompt.trim().isEmpty()) {
            fullPrompt.append("特殊要求：").append(prompt.trim()).append("\n\n");
        }

        // 分析店铺类型并添加相应的指导
        String shopType = analyzeShopType(shopDetails);

        if ("spa".equals(shopType)) {
            fullPrompt.append("文案风格要求：\n");
            fullPrompt.append("1. 适合口播的自然语言，朋友推荐的语气\n");
            fullPrompt.append("2. 突出足疗SPA的放松舒适体验\n");
            fullPrompt.append("3. 可以提到具体的服务项目和感受\n");
            fullPrompt.append("4. 语言接地气，像真实顾客分享\n");
            fullPrompt.append("5. 字数控制在50-100字\n");
            fullPrompt.append("6. 如果提到'正规绿色'要强调安全放心\n");
            fullPrompt.append("7. 如果提到'熬夜党'要突出缓解疲劳的效果\n\n");
        } else if ("restaurant".equals(shopType)) {
            fullPrompt.append("文案风格要求：\n");
            fullPrompt.append("1. 适合口播的美食推荐语气\n");
            fullPrompt.append("2. 突出菜品特色和用餐体验\n");
            fullPrompt.append("3. 语言生动有食欲感\n");
            fullPrompt.append("4. 朋友推荐的亲切语气\n");
            fullPrompt.append("5. 字数控制在50-100字\n\n");
        } else {
            fullPrompt.append("文案风格要求：\n");
            fullPrompt.append("1. 适合口播的推荐语气\n");
            fullPrompt.append("2. 突出产品或服务特色\n");
            fullPrompt.append("3. 语言自然生动\n");
            fullPrompt.append("4. 朋友分享的亲切感\n");
            fullPrompt.append("5. 字数控制在50-100字\n\n");
        }

        fullPrompt.append("请直接生成符合要求的口播文案：");

        return fullPrompt.toString();
    }

    /**
     * 分析店铺类型
     */
    private String analyzeShopType(String shopDetails) {
        if (shopDetails == null) return "general";

        String text = shopDetails.toLowerCase();

        if (text.contains("足道") || text.contains("spa") || text.contains("按摩") ||
            text.contains("理疗") || text.contains("技师") || text.contains("艾灸")) {
            return "spa";
        } else if (text.contains("餐厅") || text.contains("菜") || text.contains("食")) {
            return "restaurant";
        } else if (text.contains("美发") || text.contains("理发") || text.contains("造型")) {
            return "beauty";
        }

        return "general";
    }

    /**
     * 验证API配置 (v2版本)
     */
    @Override
    public boolean validateApiConfig() {
        try {
            // v2版本验证：尝试调用一个简单的测试请求
            String testResult = generateCopywriting(
                "请生成一句简单的测试文案",
                "测试店铺",
                50
            );
            return testResult != null && !testResult.isEmpty();
        } catch (Exception e) {
            log.error("验证百度AI API配置失败", e);
            return false;
        }
    }

    /**
     * 获取模型信息 (v2版本)
     */
    @Override
    public Map<String, Object> getModelInfo() {
        Map<String, Object> modelInfo = new HashMap<>();
        modelInfo.put("model", model);
        modelInfo.put("provider", "百度智能云 (v2版本)");
        modelInfo.put("protocol", "OpenAI兼容协议");
        modelInfo.put("baseUrl", baseUrl);
        modelInfo.put("maxTokens", 4096);
        modelInfo.put("supportBatch", true);
        modelInfo.put("authentication", "Bearer Token");
        modelInfo.put("rateLimit", "根据套餐而定");
        return modelInfo;
    }
}
