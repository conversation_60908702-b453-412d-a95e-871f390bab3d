package com.ruoyi.system.service.impl;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.IAiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.domain.AiCopywritingLibrary;
import com.ruoyi.system.domain.AiCopywritingContent;
import com.ruoyi.system.service.IAiCopywritingService;
import okhttp3.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * AI文案生成Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class AiCopywritingServiceImpl implements IAiCopywritingService 
{
    private static final Logger log = LoggerFactory.getLogger(AiCopywritingServiceImpl.class);

    @Autowired
    private IAiService aiService;

    // 模拟数据存储 (实际项目中应该使用数据库)
    private static final Map<Long, AiCopywritingLibrary> libraryStorage = new HashMap<>();
    private static final Map<Long, List<AiCopywritingContent>> contentStorage = new HashMap<>();
    private static Long libraryIdCounter = 1L;
    private static Long contentIdCounter = 1L;

    static {
        // 清空示例数据，避免与用户数据冲突
        // 不再初始化示例数据，确保用户创建的文案库不会与示例数据冲突
        libraryIdCounter = 1L;

        // 清空示例内容数据，确保用户生成的文案不会与示例数据冲突
        contentIdCounter = 1L;
    }

    /**
     * 查询文案库列表
     */
    @Override
    public List<AiCopywritingLibrary> selectLibraryList(AiCopywritingLibrary aiCopywritingLibrary)
    {
        List<AiCopywritingLibrary> result = new ArrayList<>(libraryStorage.values());
        
        // 简单的筛选逻辑
        if (aiCopywritingLibrary != null) {
            if (aiCopywritingLibrary.getLibraryName() != null && !aiCopywritingLibrary.getLibraryName().isEmpty()) {
                result = result.stream()
                    .filter(lib -> lib.getLibraryName().contains(aiCopywritingLibrary.getLibraryName()))
                    .collect(ArrayList::new, (list, item) -> list.add(item), (list1, list2) -> list1.addAll(list2));
            }
            if (aiCopywritingLibrary.getStatus() != null && !aiCopywritingLibrary.getStatus().isEmpty()) {
                result = result.stream()
                    .filter(lib -> lib.getStatus().equals(aiCopywritingLibrary.getStatus()))
                    .collect(ArrayList::new, (list, item) -> list.add(item), (list1, list2) -> list1.addAll(list2));
            }
        }
        
        // 按创建时间倒序排列
        result.sort((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()));
        
        return result;
    }

    /**
     * 查询文案库详细信息
     */
    @Override
    public AiCopywritingLibrary selectLibraryById(Long libraryId)
    {
        return libraryStorage.get(libraryId);
    }

    /**
     * 创建文案库
     */
    @Override
    public int createLibrary(AiCopywritingLibrary aiCopywritingLibrary)
    {
        try {
            aiCopywritingLibrary.setLibraryId(libraryIdCounter++);
            aiCopywritingLibrary.setGeneratedCount(0);
            aiCopywritingLibrary.setStatus(aiCopywritingLibrary.getUseAi() ? "pending" : "completed");
            aiCopywritingLibrary.setCreateTime(new Date());

            // 安全获取用户名，如果获取失败则使用默认值
            try {
                aiCopywritingLibrary.setCreateBy(SecurityUtils.getUsername());
            } catch (Exception e) {
                aiCopywritingLibrary.setCreateBy("system");
                log.warn("无法获取当前用户名，使用默认值: system");
            }

            libraryStorage.put(aiCopywritingLibrary.getLibraryId(), aiCopywritingLibrary);
            contentStorage.put(aiCopywritingLibrary.getLibraryId(), new ArrayList<>());

            log.info("创建文案库成功，ID: {}, 名称: {}", aiCopywritingLibrary.getLibraryId(), aiCopywritingLibrary.getLibraryName());

            // 如果使用AI生成，启动异步生成任务
            if (aiCopywritingLibrary.getUseAi()) {
                log.info("启动AI文案生成任务，文案库ID: {}", aiCopywritingLibrary.getLibraryId());
                generateCopywritingAsync(aiCopywritingLibrary);
            }

            return 1;
        } catch (Exception e) {
            log.error("创建文案库失败", e);
            return 0;
        }
    }

    /**
     * 修改文案库
     */
    @Override
    public int updateLibrary(AiCopywritingLibrary aiCopywritingLibrary)
    {
        try {
            AiCopywritingLibrary existing = libraryStorage.get(aiCopywritingLibrary.getLibraryId());
            if (existing != null) {
                existing.setLibraryName(aiCopywritingLibrary.getLibraryName());
                existing.setShopDetails(aiCopywritingLibrary.getShopDetails());
                existing.setPrompt(aiCopywritingLibrary.getPrompt());
                existing.setTargetCount(aiCopywritingLibrary.getTargetCount());
                existing.setWordCount(aiCopywritingLibrary.getWordCount());
                existing.setUpdateTime(new Date());

                // 安全获取用户名，如果获取失败则使用默认值
                try {
                    existing.setUpdateBy(SecurityUtils.getUsername());
                } catch (Exception e) {
                    existing.setUpdateBy("system");
                    log.warn("无法获取当前用户名，使用默认值: system");
                }
                return 1;
            }
            return 0;
        } catch (Exception e) {
            log.error("修改文案库失败", e);
            return 0;
        }
    }

    /**
     * 批量删除文案库
     */
    @Override
    public int deleteLibraryByIds(Long[] libraryIds)
    {
        try {
            int count = 0;
            for (Long libraryId : libraryIds) {
                if (libraryStorage.remove(libraryId) != null) {
                    contentStorage.remove(libraryId);
                    count++;
                }
            }
            return count;
        } catch (Exception e) {
            log.error("删除文案库失败", e);
            return 0;
        }
    }

    /**
     * 异步生成文案
     */
    @Override
    public void generateCopywritingAsync(AiCopywritingLibrary aiCopywritingLibrary)
    {
        CompletableFuture.runAsync(() -> {
            try {
                AiCopywritingLibrary library = libraryStorage.get(aiCopywritingLibrary.getLibraryId());
                if (library == null) {
                    log.error("文案库不存在: {}", aiCopywritingLibrary.getLibraryId());
                    return;
                }

                library.setStatus("generating");
                
                List<AiCopywritingContent> contents = contentStorage.computeIfAbsent(library.getLibraryId(), k -> new ArrayList<>());
                
                for (int i = 0; i < library.getTargetCount(); i++) {
                    try {
                        log.info("正在生成第{}条文案（强制使用豆包API）", i + 1);

                        // 强制调用火山引擎Doubao AI生成文案
                        String generatedContent = aiService.generateCopywriting(
                            library.getPrompt(),
                            library.getShopDetails(),
                            library.getWordCount() * 2
                        );

                        log.info("豆包API返回内容预览: {}", generatedContent.substring(0, Math.min(50, generatedContent.length())) + "...");

                        // 创建文案内容记录
                        AiCopywritingContent content = new AiCopywritingContent();
                        content.setContentId(contentIdCounter++);
                        content.setLibraryId(library.getLibraryId());
                        content.setContent(generatedContent);
                        content.setTitle("AI生成-" + (i + 1));
                        content.setWordCount(generatedContent.length());
                        content.setIsAiGenerated(true);
                        content.setStatus("active");
                        content.setQualityScore(80 + new Random().nextInt(20)); // 随机质量评分
                        content.setCreateTime(new Date());

                        contents.add(content);
                        library.setGeneratedCount(library.getGeneratedCount() + 1);

                        log.info("生成第{}条文案成功（豆包API），内容长度: {}", i + 1, generatedContent.length());

                        // 模拟生成间隔
                        Thread.sleep(2000);

                    } catch (Exception e) {
                        log.error("生成第{}条文案失败（豆包API）", i + 1, e);
                        library.setStatus("failed");
                        library.setErrorMessage("生成失败: " + e.getMessage());
                        return;
                    }
                }
                
                library.setStatus("completed");
                log.info("文案库 {} 生成完成，共生成 {} 条文案", library.getLibraryName(), library.getGeneratedCount());
                
            } catch (Exception e) {
                log.error("异步生成文案失败", e);
            }
        });
    }

    /**
     * 查询文案内容列表
     */
    @Override
    public List<AiCopywritingContent> selectContentByLibraryId(Long libraryId)
    {
        List<AiCopywritingContent> contents = contentStorage.get(libraryId);
        return contents != null ? new ArrayList<>(contents) : new ArrayList<>();
    }

    /**
     * 新增文案内容
     */
    @Override
    public int insertContent(AiCopywritingContent aiCopywritingContent)
    {
        try {
            aiCopywritingContent.setContentId(contentIdCounter++);
            aiCopywritingContent.setIsAiGenerated(false);
            aiCopywritingContent.setStatus("active");
            aiCopywritingContent.setWordCount(aiCopywritingContent.getContent().length());
            aiCopywritingContent.setCreateTime(new Date());
            aiCopywritingContent.setCreateBy(SecurityUtils.getUsername());

            List<AiCopywritingContent> contents = contentStorage.computeIfAbsent(aiCopywritingContent.getLibraryId(), k -> new ArrayList<>());
            contents.add(aiCopywritingContent);

            // 更新文案库的生成计数
            AiCopywritingLibrary library = libraryStorage.get(aiCopywritingContent.getLibraryId());
            if (library != null) {
                library.setGeneratedCount(library.getGeneratedCount() + 1);
            }

            return 1;
        } catch (Exception e) {
            log.error("新增文案内容失败", e);
            return 0;
        }
    }

    /**
     * 生成单条文案内容
     */
    @Override
    public int generateSingleContent(AiCopywritingContent aiCopywritingContent)
    {
        try {
            AiCopywritingLibrary library = libraryStorage.get(aiCopywritingContent.getLibraryId());
            if (library == null) {
                throw new RuntimeException("文案库不存在");
            }

            if (aiCopywritingContent.getUseAi()) {
                // 使用AI生成
                for (int i = 0; i < aiCopywritingContent.getCount(); i++) {
                    String generatedContent = aiService.generateCopywriting(
                        aiCopywritingContent.getPrompt() != null ? aiCopywritingContent.getPrompt() : library.getPrompt(),
                        aiCopywritingContent.getShopDetails() != null ? aiCopywritingContent.getShopDetails() : library.getShopDetails(),
                        library.getWordCount() * 2
                    );

                    AiCopywritingContent content = new AiCopywritingContent();
                    content.setContentId(contentIdCounter++);
                    content.setLibraryId(aiCopywritingContent.getLibraryId());
                    content.setContent(generatedContent);
                    content.setTitle("AI生成-" + System.currentTimeMillis());
                    content.setWordCount(generatedContent.length());
                    content.setIsAiGenerated(true);
                    content.setStatus("active");
                    content.setQualityScore(80 + new Random().nextInt(20));
                    content.setCreateTime(new Date());
                    content.setCreateBy(SecurityUtils.getUsername());

                    List<AiCopywritingContent> contents = contentStorage.computeIfAbsent(aiCopywritingContent.getLibraryId(), k -> new ArrayList<>());
                    contents.add(content);

                    library.setGeneratedCount(library.getGeneratedCount() + 1);
                }
            } else {
                // 手动添加
                return insertContent(aiCopywritingContent);
            }

            return 1;
        } catch (Exception e) {
            log.error("生成单条文案内容失败", e);
            throw new RuntimeException("生成文案失败: " + e.getMessage());
        }
    }

    /**
     * 修改文案内容
     */
    @Override
    public int updateContent(AiCopywritingContent aiCopywritingContent)
    {
        try {
            List<AiCopywritingContent> contents = contentStorage.get(aiCopywritingContent.getLibraryId());
            if (contents != null) {
                for (AiCopywritingContent content : contents) {
                    if (content.getContentId().equals(aiCopywritingContent.getContentId())) {
                        content.setContent(aiCopywritingContent.getContent());
                        content.setTitle(aiCopywritingContent.getTitle());
                        content.setWordCount(aiCopywritingContent.getContent().length());
                        content.setUpdateTime(new Date());
                        content.setUpdateBy(SecurityUtils.getUsername());
                        return 1;
                    }
                }
            }
            return 0;
        } catch (Exception e) {
            log.error("修改文案内容失败", e);
            return 0;
        }
    }

    /**
     * 批量删除文案内容
     */
    @Override
    public int deleteContentByIds(Long[] contentIds)
    {
        try {
            int count = 0;
            for (Long contentId : contentIds) {
                for (List<AiCopywritingContent> contents : contentStorage.values()) {
                    if (contents.removeIf(content -> content.getContentId().equals(contentId))) {
                        count++;
                        break;
                    }
                }
            }
            return count;
        } catch (Exception e) {
            log.error("删除文案内容失败", e);
            return 0;
        }
    }

    /**
     * 获取生成进度
     */
    @Override
    public Map<String, Object> getGenerationProgress(Long libraryId)
    {
        Map<String, Object> progress = new HashMap<>();
        AiCopywritingLibrary library = libraryStorage.get(libraryId);

        if (library != null) {
            progress.put("libraryId", libraryId);
            progress.put("status", library.getStatus());
            progress.put("targetCount", library.getTargetCount());
            progress.put("generatedCount", library.getGeneratedCount());
            progress.put("progress", library.getTargetCount() > 0 ?
                (library.getGeneratedCount() * 100 / library.getTargetCount()) : 0);
            progress.put("errorMessage", library.getErrorMessage());
        }

        return progress;
    }

    /**
     * 重新生成文案库
     */
    @Override
    public void regenerateLibrary(Long libraryId)
    {
        AiCopywritingLibrary library = libraryStorage.get(libraryId);
        if (library != null) {
            // 清空现有内容
            contentStorage.put(libraryId, new ArrayList<>());
            library.setGeneratedCount(0);
            library.setStatus("pending");
            library.setErrorMessage(null);

            // 重新生成
            generateCopywritingAsync(library);
        }
    }
}
