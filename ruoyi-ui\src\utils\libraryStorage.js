/**
 * 文案库localStorage管理工具
 * 确保文案库数据的持久化存储
 */

const STORAGE_KEYS = {
  DAKA: 'daka_libraries',
  DOU: 'dou_libraries', 
  HONG: 'hong_libraries',
  SHIPIN: 'shipin_libraries'
}

class LibraryStorage {
  /**
   * 保存文案库列表
   * @param {string} type - 文案库类型 (daka/dou/hong/shipin)
   * @param {Array} libraries - 文案库列表
   */
  saveLibraries(type, libraries) {
    try {
      const key = STORAGE_KEYS[type.toUpperCase()]
      if (!key) {
        console.error('❌ 无效的文案库类型:', type)
        return false
      }
      
      const data = JSON.stringify(libraries)
      localStorage.setItem(key, data)
      console.log(`✅ ${type}文案库已保存到localStorage:`, libraries.length, '个文案库')
      console.log(`📝 保存的键名: ${key}`)
      
      // 立即验证保存是否成功
      const saved = localStorage.getItem(key)
      if (saved) {
        console.log(`✅ 验证保存成功: ${key}`)
        return true
      } else {
        console.error(`❌ 验证保存失败: ${key}`)
        return false
      }
    } catch (error) {
      console.error(`❌ 保存${type}文案库失败:`, error)
      return false
    }
  }

  /**
   * 加载文案库列表
   * @param {string} type - 文案库类型 (daka/dou/hong/shipin)
   * @returns {Array} 文案库列表
   */
  loadLibraries(type) {
    try {
      const key = STORAGE_KEYS[type.toUpperCase()]
      if (!key) {
        console.error('❌ 无效的文案库类型:', type)
        return []
      }
      
      const data = localStorage.getItem(key)
      console.log(`🔍 从localStorage加载${type}文案库:`, key, data ? '有数据' : '无数据')
      
      if (data) {
        const libraries = JSON.parse(data)
        console.log(`✅ 成功加载${type}文案库:`, libraries.length, '个文案库')
        return libraries
      } else {
        console.log(`📝 ${type}文案库为空`)
        return []
      }
    } catch (error) {
      console.error(`❌ 加载${type}文案库失败:`, error)
      return []
    }
  }

  /**
   * 添加单个文案库
   * @param {string} type - 文案库类型
   * @param {Object} library - 文案库对象
   */
  addLibrary(type, library) {
    const libraries = this.loadLibraries(type)
    libraries.unshift(library)
    return this.saveLibraries(type, libraries)
  }

  /**
   * 删除文案库
   * @param {string} type - 文案库类型
   * @param {number} libraryId - 文案库ID
   */
  removeLibrary(type, libraryId) {
    const libraries = this.loadLibraries(type)
    const filtered = libraries.filter(lib => lib.libraryId !== libraryId)
    return this.saveLibraries(type, filtered)
  }

  /**
   * 更新文案库
   * @param {string} type - 文案库类型
   * @param {number} libraryId - 文案库ID
   * @param {Object} updates - 更新的数据
   */
  updateLibrary(type, libraryId, updates) {
    const libraries = this.loadLibraries(type)
    const index = libraries.findIndex(lib => lib.libraryId === libraryId)
    if (index !== -1) {
      libraries[index] = { ...libraries[index], ...updates }
      return this.saveLibraries(type, libraries)
    }
    return false
  }

  /**
   * 检查localStorage状态
   */
  checkStatus() {
    console.log('📊 localStorage状态检查:')
    Object.entries(STORAGE_KEYS).forEach(([type, key]) => {
      const data = localStorage.getItem(key)
      if (data) {
        try {
          const parsed = JSON.parse(data)
          console.log(`✅ ${type}: ${parsed.length} 个文案库`)
        } catch (e) {
          console.log(`❌ ${type}: 数据损坏`)
        }
      } else {
        console.log(`📝 ${type}: 无数据`)
      }
    })
  }

  /**
   * 清理所有文案库数据（谨慎使用）
   */
  clearAll() {
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key)
    })
    console.log('🗑️ 已清理所有文案库数据')
  }
}

// 创建单例实例
const libraryStorage = new LibraryStorage()

export default libraryStorage
