import request from '@/utils/request'

// 测试API - 生成AI文案
export function testGenerate(data) {
  return request({
    url: '/ai/test/generate',
    method: 'post',
    data: data,
    timeout: 60000 // 60秒超时
  })
}

// 测试API - 获取文案库列表
export function listLibraryTest() {
  return request({
    url: '/ai/test/library/list',
    method: 'get'
  })
}

// 测试API - 创建文案库
export function addLibraryTest(data) {
  return request({
    url: '/ai/test/library/add',
    method: 'post',
    data: data
  })
}

// 测试API - 健康检查
export function healthCheck() {
  return request({
    url: '/ai/test/health',
    method: 'get'
  })
}

// 测试API - 直接豆包服务测试
export function testDoubaoDirectService() {
  return request({
    url: '/ai/test/test-doubao-direct',
    method: 'get'
  })
}

// 测试API - 生成文案（测试版本）
export function generateCopywritingTest(data) {
  return request({
    url: '/ai/copywriting/generate/test',
    method: 'post',
    data: data,
    timeout: 60000
  })
}

// 测试API - DeepSeek直接测试
export function testDeepSeekDirect(data) {
  return request({
    url: '/ai/test/deepseek-direct',
    method: 'post',
    data: data,
    timeout: 60000
  })
}
